"use client";

import { useState } from "react";
import { useTranslations } from "next-intl";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from "@/components/ui/dialog";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { Trash, Shield } from "lucide-react";
import {
	DataTable,
	type Column,
	type FilterOption,
	type Action,
} from "./data-table";
import { StatusBadge } from "./status-badge";
import { api } from "@/lib/trpc/react";

interface User {
	id: number;
	name: string;
	email: string;
	role: "admin" | "adopter" | "rescuer" | "clinic";
	image: string | null;
	createdAt: Date;
	wilaya?: { name: string } | null;
	commune?: { name: string } | null;
}

interface AdminUsersListProps {
	limit?: number;
}

export function AdminUsersList({ limit }: AdminUsersListProps) {
	const t = useTranslations("admin");
	const { toast } = useToast();

	// State for table controls
	const [search, setSearch] = useState("");
	const [roleFilter, setRoleFilter] = useState("");
	const [sortBy, setSortBy] = useState<
		"name" | "email" | "createdAt" | "role"
	>("createdAt");
	const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");
	const [page, setPage] = useState(0);
	const pageSize = limit || 20;

	// State for dialogs
	const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
	const [roleDialogOpen, setRoleDialogOpen] = useState(false);
	const [selectedUser, setSelectedUser] = useState<User | null>(null);
	const [newRole, setNewRole] = useState<
		"admin" | "adopter" | "rescuer" | "clinic"
	>("adopter");

	// API queries and mutations
	const {
		data: usersData,
		isLoading,
		refetch,
	} = api.admin.listUsers.useQuery({
		limit: pageSize,
		offset: page * pageSize,
		search: search || undefined,
		role: (roleFilter as any) || undefined,
		sortBy,
		sortOrder,
	});

	const updateRoleMutation = api.admin.updateUserRole.useMutation({
		onSuccess: () => {
			toast({
				title: t("users.roleUpdated"),
				description: t("users.roleUpdatedDescription"),
			});
			refetch();
			setRoleDialogOpen(false);
		},
		onError: (error) => {
			toast({
				title: t("common.error"),
				description: error.message,
				variant: "destructive",
			});
		},
	});

	const deleteUserMutation = api.admin.deleteUser.useMutation({
		onSuccess: () => {
			toast({
				title: t("users.userDeleted"),
				description: t("users.userDeletedDescription"),
			});
			refetch();
			setDeleteDialogOpen(false);
		},
		onError: (error) => {
			toast({
				title: t("common.error"),
				description: error.message,
				variant: "destructive",
			});
		},
	});

	// Table configuration
	const columns: Column<User>[] = [
		{
			key: "user",
			label: t("users.table.user"),
			render: (user) => (
				<div className="flex items-center space-x-3">
					<div className="relative h-10 w-10 rounded-full overflow-hidden bg-muted">
						<Image
							src={
								user.image ||
								`https://placehold.co/40x40/e2e8f0/94a3b8?text=${user.name.charAt(0)}`
							}
							alt={user.name}
							fill
							className="object-cover"
							onError={(e) => {
								e.currentTarget.src = `https://placehold.co/40x40/e2e8f0/94a3b8?text=${user.name.charAt(0)}`;
							}}
						/>
					</div>
					<div>
						<div className="font-medium">{user.name}</div>
						<div className="text-sm text-muted-foreground">
							{user.email}
						</div>
					</div>
				</div>
			),
		},
		{
			key: "role",
			label: t("users.table.role"),
			sortable: true,
			render: (user) => <StatusBadge status={user.role} type="user" />,
		},
		{
			key: "location",
			label: t("users.table.location"),
			render: (user) => (
				<div className="text-sm">
					{user.wilaya?.name && user.commune?.name
						? `${user.commune.name}, ${user.wilaya.name}`
						: t("common.notSpecified")}
				</div>
			),
		},
		{
			key: "createdAt",
			label: t("users.table.joinedDate"),
			sortable: true,
			render: (user) => (
				<div className="text-sm">
					{new Date(user.createdAt).toLocaleDateString()}
				</div>
			),
		},
	];

	const filters: FilterOption[] = [
		{
			key: "role",
			label: t("users.filters.role"),
			options: [
				{ value: "admin", label: t("roles.admin") },
				{ value: "rescuer", label: t("roles.rescuer") },
				{ value: "clinic", label: t("roles.clinic") },
				{ value: "adopter", label: t("roles.adopter") },
			],
		},
	];

	const actions: Action<User>[] = [
		{
			label: t("users.actions.changeRole"),
			icon: <Shield className="h-4 w-4" />,
			onClick: (user) => {
				setSelectedUser(user);
				setNewRole(user.role);
				setRoleDialogOpen(true);
			},
		},
		{
			label: t("users.actions.delete"),
			icon: <Trash className="h-4 w-4" />,
			variant: "destructive",
			onClick: (user) => {
				setSelectedUser(user);
				setDeleteDialogOpen(true);
			},
		},
	];

	const handleRoleUpdate = () => {
		if (!selectedUser) return;

		updateRoleMutation.mutate({
			userId: selectedUser.id,
			role: newRole,
		});
	};

	const handleUserDelete = () => {
		if (!selectedUser) return;

		deleteUserMutation.mutate({
			userId: selectedUser.id,
		});
	};

	const users = usersData?.users || [];

	return (
		<div className="space-y-4">
			<DataTable
				data={users}
				columns={columns}
				loading={isLoading}
				searchPlaceholder={t("users.searchPlaceholder")}
				searchValue={search}
				onSearchChange={setSearch}
				filters={filters}
				filterValues={{ role: roleFilter }}
				onFilterChange={(key, value) => {
					if (key === "role") setRoleFilter(value);
				}}
				sortBy={sortBy}
				sortOrder={sortOrder}
				onSortChange={(key, order) => {
					setSortBy(key as any);
					setSortOrder(order);
				}}
				actions={actions}
				pagination={
					usersData
						? {
								total: usersData.total,
								limit: pageSize,
								offset: page * pageSize,
								onPageChange: (offset) =>
									setPage(offset / pageSize),
							}
						: undefined
				}
				emptyMessage={t("users.noUsers")}
			/>

			{/* Role Change Dialog */}
			<Dialog open={roleDialogOpen} onOpenChange={setRoleDialogOpen}>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>{t("users.changeRole")}</DialogTitle>
						<DialogDescription>
							{t("users.changeRoleDescription", {
								name: selectedUser?.name || "",
							})}
						</DialogDescription>
					</DialogHeader>
					<div className="py-4">
						<Select
							value={newRole}
							onValueChange={(value: any) => setNewRole(value)}
						>
							<SelectTrigger>
								<SelectValue />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value="admin">
									{t("roles.admin")}
								</SelectItem>
								<SelectItem value="rescuer">
									{t("roles.rescuer")}
								</SelectItem>
								<SelectItem value="clinic">
									{t("roles.clinic")}
								</SelectItem>
								<SelectItem value="adopter">
									{t("roles.adopter")}
								</SelectItem>
							</SelectContent>
						</Select>
					</div>
					<DialogFooter>
						<Button
							variant="outline"
							onClick={() => setRoleDialogOpen(false)}
						>
							{t("common.cancel")}
						</Button>
						<Button
							onClick={handleRoleUpdate}
							disabled={updateRoleMutation.isPending}
						>
							{updateRoleMutation.isPending
								? t("common.updating")
								: t("common.update")}
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>

			{/* Delete Confirmation Dialog */}
			<Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>{t("users.deleteUser")}</DialogTitle>
						<DialogDescription>
							{t("users.deleteUserDescription", {
								name: selectedUser?.name || "",
							})}
						</DialogDescription>
					</DialogHeader>
					<DialogFooter>
						<Button
							variant="outline"
							onClick={() => setDeleteDialogOpen(false)}
						>
							{t("common.cancel")}
						</Button>
						<Button
							variant="destructive"
							onClick={handleUserDelete}
							disabled={deleteUserMutation.isPending}
						>
							{deleteUserMutation.isPending
								? t("common.deleting")
								: t("common.delete")}
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>
		</div>
	);
}
