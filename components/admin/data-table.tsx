"use client";

import { useState } from "react";
import { useTranslations } from "next-intl";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/ui/table";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuLabel,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import {
	ChevronLeft,
	ChevronRight,
	Search,
	MoreHorizontal,
	ArrowUpDown,
	ArrowUp,
	ArrowDown,
} from "lucide-react";

export interface Column<T> {
	key: string;
	label: string;
	sortable?: boolean;
	render?: (item: T) => React.ReactNode;
	className?: string;
}

export interface FilterOption {
	key: string;
	label: string;
	options: { value: string; label: string }[];
}

export interface Action<T> {
	label: string;
	onClick: (item: T) => void;
	variant?: "default" | "destructive";
	icon?: React.ReactNode;
}

interface DataTableProps<T> {
	data: T[];
	columns: Column<T>[];
	loading?: boolean;
	searchPlaceholder?: string;
	searchValue?: string;
	onSearchChange?: (value: string) => void;
	filters?: FilterOption[];
	filterValues?: Record<string, string>;
	onFilterChange?: (key: string, value: string) => void;
	sortBy?: string;
	sortOrder?: "asc" | "desc";
	onSortChange?: (key: string, order: "asc" | "desc") => void;
	actions?: Action<T>[];
	pagination?: {
		total: number;
		limit: number;
		offset: number;
		onPageChange: (offset: number) => void;
	};
	emptyMessage?: string;
	className?: string;
}

export function DataTable<T extends Record<string, any>>({
	data,
	columns,
	loading = false,
	searchPlaceholder = "Search...",
	searchValue = "",
	onSearchChange,
	filters = [],
	filterValues = {},
	onFilterChange,
	sortBy,
	sortOrder,
	onSortChange,
	actions = [],
	pagination,
	emptyMessage = "No data available",
	className,
}: DataTableProps<T>) {
	const t = useTranslations("admin");

	const handleSort = (key: string) => {
		if (!onSortChange) return;
		
		if (sortBy === key) {
			onSortChange(key, sortOrder === "asc" ? "desc" : "asc");
		} else {
			onSortChange(key, "asc");
		}
	};

	const getSortIcon = (key: string) => {
		if (sortBy !== key) return <ArrowUpDown className="h-4 w-4" />;
		return sortOrder === "asc" ? 
			<ArrowUp className="h-4 w-4" /> : 
			<ArrowDown className="h-4 w-4" />;
	};

	const currentPage = pagination ? Math.floor(pagination.offset / pagination.limit) + 1 : 1;
	const totalPages = pagination ? Math.ceil(pagination.total / pagination.limit) : 1;

	return (
		<div className={`space-y-4 ${className}`}>
			{/* Search and Filters */}
			<div className="flex flex-col sm:flex-row gap-4">
				{onSearchChange && (
					<div className="relative flex-1">
						<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
						<Input
							placeholder={searchPlaceholder}
							value={searchValue}
							onChange={(e) => onSearchChange(e.target.value)}
							className="pl-10"
						/>
					</div>
				)}
				
				{filters.map((filter) => (
					<Select
						key={filter.key}
						value={filterValues[filter.key] || ""}
						onValueChange={(value) => onFilterChange?.(filter.key, value)}
					>
						<SelectTrigger className="w-full sm:w-[180px]">
							<SelectValue placeholder={filter.label} />
						</SelectTrigger>
						<SelectContent>
							<SelectItem value="">{t("filters.all")}</SelectItem>
							{filter.options.map((option) => (
								<SelectItem key={option.value} value={option.value}>
									{option.label}
								</SelectItem>
							))}
						</SelectContent>
					</Select>
				))}
			</div>

			{/* Table */}
			<div className="border rounded-md">
				<Table>
					<TableHeader>
						<TableRow>
							{columns.map((column) => (
								<TableHead
									key={column.key}
									className={`${column.className} ${
										column.sortable ? "cursor-pointer hover:bg-muted/50" : ""
									}`}
									onClick={() => column.sortable && handleSort(column.key)}
								>
									<div className="flex items-center gap-2">
										{column.label}
										{column.sortable && getSortIcon(column.key)}
									</div>
								</TableHead>
							))}
							{actions.length > 0 && (
								<TableHead className="w-[100px]">{t("table.actions")}</TableHead>
							)}
						</TableRow>
					</TableHeader>
					<TableBody>
						{loading ? (
							// Loading skeleton
							Array.from({ length: 5 }).map((_, index) => (
								<TableRow key={index}>
									{columns.map((column) => (
										<TableCell key={column.key}>
											<Skeleton className="h-4 w-full" />
										</TableCell>
									))}
									{actions.length > 0 && (
										<TableCell>
											<Skeleton className="h-8 w-8 rounded" />
										</TableCell>
									)}
								</TableRow>
							))
						) : data.length === 0 ? (
							// Empty state
							<TableRow>
								<TableCell
									colSpan={columns.length + (actions.length > 0 ? 1 : 0)}
									className="text-center py-8 text-muted-foreground"
								>
									{emptyMessage}
								</TableCell>
							</TableRow>
						) : (
							// Data rows
							data.map((item, index) => (
								<TableRow key={item.id || index}>
									{columns.map((column) => (
										<TableCell key={column.key} className={column.className}>
											{column.render ? column.render(item) : item[column.key]}
										</TableCell>
									))}
									{actions.length > 0 && (
										<TableCell>
											<DropdownMenu>
												<DropdownMenuTrigger asChild>
													<Button variant="ghost" size="icon">
														<MoreHorizontal className="h-4 w-4" />
														<span className="sr-only">Open menu</span>
													</Button>
												</DropdownMenuTrigger>
												<DropdownMenuContent align="end">
													<DropdownMenuLabel>{t("table.actions")}</DropdownMenuLabel>
													<DropdownMenuSeparator />
													{actions.map((action, actionIndex) => (
														<DropdownMenuItem
															key={actionIndex}
															onClick={() => action.onClick(item)}
															className={
																action.variant === "destructive"
																	? "text-destructive focus:text-destructive"
																	: ""
															}
														>
															{action.icon && (
																<span className="mr-2">{action.icon}</span>
															)}
															{action.label}
														</DropdownMenuItem>
													))}
												</DropdownMenuContent>
											</DropdownMenu>
										</TableCell>
									)}
								</TableRow>
							))
						)}
					</TableBody>
				</Table>
			</div>

			{/* Pagination */}
			{pagination && (
				<div className="flex items-center justify-between">
					<div className="text-sm text-muted-foreground">
						{t("table.showing", {
							start: pagination.offset + 1,
							end: Math.min(pagination.offset + pagination.limit, pagination.total),
							total: pagination.total,
						})}
					</div>
					<div className="flex items-center gap-2">
						<Button
							variant="outline"
							size="sm"
							onClick={() => pagination.onPageChange(pagination.offset - pagination.limit)}
							disabled={pagination.offset === 0}
						>
							<ChevronLeft className="h-4 w-4" />
							{t("table.previous")}
						</Button>
						<div className="text-sm">
							{t("table.page", { current: currentPage, total: totalPages })}
						</div>
						<Button
							variant="outline"
							size="sm"
							onClick={() => pagination.onPageChange(pagination.offset + pagination.limit)}
							disabled={pagination.offset + pagination.limit >= pagination.total}
						>
							{t("table.next")}
							<ChevronRight className="h-4 w-4" />
						</Button>
					</div>
				</div>
			)}
		</div>
	);
}
